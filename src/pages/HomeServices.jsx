import { useState } from "react";
import { FiSend } from "react-icons/fi";
import toast from "react-hot-toast";
import { formService } from "../services/api";
import { lagosLocationData } from "../utils/locations";

export default function HomeServices() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    service: "",
    customService: "",
    description: "",
    address: {
      street: "",
      state: "Lagos",
      localGovernment: "",
      area: "",
    },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const serviceOptions = [
    "Cleaning",
    "Plumbing",
    "Electrical",
    "Carpentry",
    "Painting",
    "Gardening",
    "Appliance Repair",
    "HVAC",
    "Locksmith",
    "Pest Control",
    "Furniture Assembly",
    "Moving Services",
    "Interior Design",
    "Home Security",
    "Other",
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name.startsWith("address.")) {
      const addressField = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
          // Reset dependent fields when parent changes
          ...(addressField === "localGovernment" && { area: "" }),
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await formService.submitHomeServiceForm(formData);
      toast.success("Your service request has been submitted successfully!");
      setFormData({
        name: "",
        email: "",
        phone: "",
        service: "",
        customService: "",
        description: "",
        address: {
          street: "",
          state: "Lagos",
          localGovernment: "",
          area: "",
        },
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit your request. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section - HomeFix */}
      <section className="bg-primary-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                HomeFix
                {/* Your trusted solution for Hassle-free Home Repairs */}
              </h1>
              <p className="text-xl text-primary-100 mb-8 leading-relaxed">
                With HomeFix, we connect you with vetted professional artisans
                at the click of a button. Whether it's plumbing, electrical
                work, carpentry, appliance repairs or name it, we ensure fast,
                cost affordable, and reliable service from experienced hands.
                Say goodbye to unverified technicians and hidden charges. With
                Aplet360, you enjoy seamless service booking, impeccable
                deliver, and transparent pricing, all from the comfort of your
                home.
              </p>
              <p className="text-lg text-primary-100 font-semibold">
                HomeFix is your go-to service on Aplet360 for getting things
                done right, every time. One click, and we fix it!
              </p>
            </div>
            <div className="flex justify-center">
              <img
                src="/images/professional-technician.jpg"
                alt="Professional technician ready to fix home"
                className="rounded-lg shadow-lg max-w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Service Request Form */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-white p-8 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold mb-6 text-center">
              Request a Service
            </h2>
            <p className="text-tertiary-600 mb-8 text-center">
              Fill out the form below to request a home service. Our team of
              professionals will get back to you shortly.
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-tertiary-700 mb-1"
                  >
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-tertiary-700 mb-1"
                  >
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                    placeholder="Your email address"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-tertiary-700 mb-1"
                  >
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                    placeholder="Your phone number"
                  />
                </div>
                <div>
                  <label
                    htmlFor="service"
                    className="block text-sm font-medium text-tertiary-700 mb-1"
                  >
                    Service Type *
                  </label>
                  <select
                    id="service"
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                  >
                    <option value="">Select a service</option>
                    {serviceOptions.map((service) => (
                      <option key={service} value={service}>
                        {service}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Address Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-tertiary-900 border-b border-tertiary-200 pb-2">
                  Service Address
                </h3>

                <div>
                  <label
                    htmlFor="street"
                    className="block text-sm font-medium text-tertiary-700 mb-1"
                  >
                    Street Number and Name *
                  </label>
                  <input
                    type="text"
                    id="street"
                    name="address.street"
                    value={formData.address.street}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                    placeholder="e.g., 123 Main Street"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label
                      htmlFor="state"
                      className="block text-sm font-medium text-tertiary-700 mb-1"
                    >
                      State *
                    </label>
                    <select
                      id="state"
                      name="address.state"
                      value={formData.address.state}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                    >
                      <option value="Lagos">Lagos</option>
                    </select>
                    <p className="text-xs text-tertiary-500 mt-1">
                      Currently available in Lagos only
                    </p>
                  </div>

                  <div>
                    <label
                      htmlFor="localGovernment"
                      className="block text-sm font-medium text-tertiary-700 mb-1"
                    >
                      Local Government *
                    </label>
                    <select
                      id="localGovernment"
                      name="address.localGovernment"
                      value={formData.address.localGovernment}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                    >
                      <option value="">Select LGA</option>
                      {Object.keys(lagosLocationData).map((lga) => (
                        <option key={lga} value={lga}>
                          {lga}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label
                      htmlFor="area"
                      className="block text-sm font-medium text-tertiary-700 mb-1"
                    >
                      Area *
                    </label>
                    <select
                      id="area"
                      name="address.area"
                      value={formData.address.area}
                      onChange={handleChange}
                      required
                      disabled={!formData.address.localGovernment}
                      className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900 disabled:bg-tertiary-100 disabled:cursor-not-allowed"
                    >
                      <option value="">Select Area</option>
                      {formData.address.localGovernment &&
                        lagosLocationData[
                          formData.address.localGovernment
                        ]?.map((area) => (
                          <option key={area} value={area}>
                            {area}
                          </option>
                        ))}
                    </select>
                    {!formData.address.localGovernment && (
                      <p className="text-xs text-tertiary-500 mt-1">
                        Please select a Local Government first
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="block text-sm font-medium text-tertiary-700 mb-1"
                >
                  Service Description *
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows={5}
                  className="w-full px-4 py-2 border border-tertiary-300 rounded-md focus:ring-primary-900 focus:border-primary-900"
                  placeholder="Please describe what you need help with..."
                ></textarea>
              </div>

              <div className="flex justify-center">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-900 hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-900 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    "Submitting..."
                  ) : (
                    <>
                      <FiSend className="mr-2" /> Submit Request
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 bg-tertiary-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">
            Our Home Services
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {serviceOptions.slice(0, 9).map((service) => (
              <div
                key={service}
                className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
              >
                <h3 className="text-xl font-semibold mb-3 text-primary-900">
                  {service}
                </h3>
                <p className="text-tertiary-600">
                  Professional {service.toLowerCase()} services for your home,
                  delivered by our vetted and skilled artisans.
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
